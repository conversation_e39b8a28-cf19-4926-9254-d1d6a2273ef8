<template>
  <div class="flex flex-col h-full w-full p-6">
    <div class="flex items-center mb-6">
      <Button variant="ghost" size="icon" class="mr-2" @click="goBack">
        <Icon icon="lucide:arrow-left" class="w-5 h-5" />
      </Button>
      <h1 class="text-xl font-bold">{{ t('gpuInfo.title') }}</h1>
    </div>

    <div class="flex-1 overflow-y-auto">
      <!-- 加载状态 -->
      <div v-if="loading" class="flex items-center justify-center h-64">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-foreground"></div>
        <span class="ml-3">{{ t('gpuInfo.loading') }}</span>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="bg-destructive/20 border border-destructive/30 rounded-lg p-4 mb-6">
        <div class="flex items-center">
          <Icon icon="lucide:alert-circle" class="w-5 h-5 text-destructive mr-2" />
          <span class="font-medium text-destructive">{{ t('gpuInfo.error') }}</span>
        </div>
        <p class="mt-2 text-sm text-destructive/80">{{ error }}</p>
        <Button variant="outline" size="sm" class="mt-3" @click="refreshData">
          <Icon icon="lucide:refresh-cw" class="w-4 h-4 mr-2" />
          {{ t('gpuInfo.retry') }}
        </Button>
      </div>

      <!-- 显卡信息 -->
      <div v-else>
        <div v-for="(gpu, index) in gpuData" :key="index" class="bg-card border rounded-lg p-5 mb-6 shadow-sm">
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
              <div class="bg-primary/10 p-2 rounded-lg mr-3">
                <Icon icon="lucide:chip" class="w-6 h-6 text-primary" />
              </div>
              <div>
                <h2 class="font-bold text-lg">{{ gpu.name || t('gpuInfo.gpu') + ' ' + (index + 1) }}</h2>
                <p class="text-sm text-muted-foreground">{{ gpu.driverVersion }}</p>
              </div>
            </div>
            <Badge variant="secondary">{{ gpu.status }}</Badge>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
            <!-- 显存使用情况 -->
            <div class="bg-muted/50 rounded-lg p-4">
              <div class="flex justify-between items-center mb-2">
                <h3 class="font-medium">{{ t('gpuInfo.memory') }}</h3>
                <span class="text-sm font-mono">{{ formatBytes(gpu.memoryUsed) }} / {{ formatBytes(gpu.memoryTotal) }}</span>
              </div>
              <div class="w-full bg-secondary rounded-full h-2.5">
                <div 
                  class="bg-primary h-2.5 rounded-full" 
                  :style="{ width: gpu.memoryUtilization + '%' }"
                ></div>
              </div>
              <div class="flex justify-between text-xs text-muted-foreground mt-1">
                <span>{{ gpu.memoryUtilization.toFixed(1) }}%</span>
                <span>{{ t('gpuInfo.utilization') }}</span>
              </div>
            </div>

            <!-- GPU使用率 -->
            <div class="bg-muted/50 rounded-lg p-4">
              <div class="flex justify-between items-center mb-2">
                <h3 class="font-medium">{{ t('gpuInfo.gpuUtilization') }}</h3>
                <span class="text-sm font-mono">{{ gpu.gpuUtilization }}%</span>
              </div>
              <div class="w-full bg-secondary rounded-full h-2.5">
                <div 
                  class="bg-primary h-2.5 rounded-full" 
                  :style="{ width: gpu.gpuUtilization + '%' }"
                ></div>
              </div>
              <div class="flex justify-between text-xs text-muted-foreground mt-1">
                <span>{{ gpu.gpuUtilization }}%</span>
                <span>{{ t('gpuInfo.utilization') }}</span>
              </div>
            </div>

            <!-- 温度 -->
            <div class="bg-muted/50 rounded-lg p-4">
              <div class="flex justify-between items-center">
                <div class="flex items-center">
                  <Icon icon="lucide:thermometer" class="w-4 h-4 mr-2 text-muted-foreground" />
                  <h3 class="font-medium">{{ t('gpuInfo.temperature') }}</h3>
                </div>
                <span class="text-sm font-mono">{{ gpu.temperature }}°C</span>
              </div>
              <div class="mt-2 text-xs text-muted-foreground">
                {{ t('gpuInfo.fanSpeed') }}: {{ gpu.fanSpeed }}%
              </div>
            </div>

            <!-- 功耗 -->
            <div class="bg-muted/50 rounded-lg p-4">
              <div class="flex justify-between items-center">
                <div class="flex items-center">
                  <Icon icon="lucide:zap" class="w-4 h-4 mr-2 text-muted-foreground" />
                  <h3 class="font-medium">{{ t('gpuInfo.power') }}</h3>
                </div>
                <span class="text-sm font-mono">{{ gpu.powerDraw }}W / {{ gpu.powerLimit }}W</span>
              </div>
              <div class="mt-2 text-xs text-muted-foreground">
                {{ t('gpuInfo.powerUtilization') }}: {{ gpu.powerUtilization }}%
              </div>
            </div>
          </div>

          <!-- 详细信息 -->
          <div class="mt-6 pt-4 border-t">
            <h3 class="font-medium mb-3">{{ t('gpuInfo.details') }}</h3>
            <div class="grid grid-cols-2 gap-3 text-sm">
              <div>
                <span class="text-muted-foreground">{{ t('gpuInfo.pcie') }}:</span>
                <span class="ml-2">{{ gpu.pcieLink }}</span>
              </div>
              <div>
                <span class="text-muted-foreground">{{ t('gpuInfo.clock') }}:</span>
                <span class="ml-2">{{ gpu.graphicsClock }} MHz</span>
              </div>
              <div>
                <span class="text-muted-foreground">{{ t('gpuInfo.memoryClock') }}:</span>
                <span class="ml-2">{{ gpu.memoryClock }} MHz</span>
              </div>
              <div>
                <span class="text-muted-foreground">{{ t('gpuInfo.processes') }}:</span>
                <span class="ml-2">{{ gpu.processes.length }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 刷新按钮 -->
    <div class="flex justify-end mt-4">
      <Button variant="outline" size="sm" @click="refreshData" :disabled="loading">
        <Icon 
          icon="lucide:refresh-cw" 
          class="w-4 h-4 mr-2" 
          :class="{ 'animate-spin': loading }"
        />
        {{ t('gpuInfo.refresh') }}
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Icon } from '@iconify/vue'

const { t } = useI18n()
const router = useRouter()

// GPU数据状态
const gpuData = ref<any[]>([])
const loading = ref(true)
const error = ref<string | null>(null)
const refreshInterval = ref<number | null>(null)
const isSupported = ref<boolean>(true)

// 格式化字节大小
const formatBytes = (bytes: number, decimals = 2): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 检查GPU支持
const checkGpuSupport = async () => {
  try {
    isSupported.value = await window.electron.ipcRenderer.invoke('gpu:is-supported')
    return isSupported.value
  } catch (err) {
    console.error('Failed to check GPU support:', err)
    isSupported.value = false
    return false
  }
}

// 获取GPU信息
const fetchGPUInfo = async () => {
  try {
    loading.value = true
    error.value = null
    
    // 检查GPU支持
    const supported = await checkGpuSupport()
    if (!supported) {
      error.value = t('gpuInfo.notSupported')
      return
    }
    
    // 调用主进程的GPU Presenter获取真实数据
    const gpuDataResponse = await window.electron.ipcRenderer.invoke('gpu:get-info')
    gpuData.value = gpuDataResponse
  } catch (err) {
    console.error('Failed to fetch GPU info:', err)
    error.value = err instanceof Error ? err.message : String(err)
  } finally {
    loading.value = false
  }
}

// 刷新数据
const refreshData = () => {
  fetchGPUInfo()
}

// 启动定时刷新
const startAutoRefresh = () => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
  }
  refreshInterval.value = window.setInterval(refreshData, 3000) // 每3秒刷新
}

// 停止定时刷新
const stopAutoRefresh = () => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
    refreshInterval.value = null
  }
}

// 组件挂载时获取数据并启动定时刷新
onMounted(() => {
  fetchGPUInfo()
  startAutoRefresh()
})

// 组件卸载时停止定时刷新
onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
/* 可以添加自定义样式 */
</style>