// 简单的测试脚本来直接测试GPU Presenter的功能
import { exec } from 'child_process';
import { promisify } from 'util';

const execPromise = promisify(exec);

async function testGpuPresenterFunctionality() {
  try {
    console.log('测试GPU Presenter功能...');
    
    // 模拟GpuPresenter中的isGpuMonitoringSupported方法
    console.log('1. 检查NVIDIA驱动支持...');
    try {
      await execPromise('nvidia-smi --version');
      console.log('   ✓ NVIDIA驱动已安装并可用');
    } catch (error) {
      console.log('   ✗ NVIDIA驱动不可用:', error.message);
      return;
    }
    
    // 模拟GpuPresenter中的getNvidiaGpuInfo方法
    console.log('2. 获取GPU信息...');
    try {
      const { stdout } = await execPromise(
        'nvidia-smi --query-gpu=index,name,driver_version,memory.total,memory.used,utilization.gpu,temperature.gpu,fan.speed,power.draw,power.limit,pcie.link.gen.current,clocks.current.graphics,clocks.current.memory --format=csv,noheader,nounits'
      );
      
      const lines = stdout.trim().split('\n');
      const gpuData = [];
      
      for (const line of lines) {
        const [
          index,
          name,
          driverVersion,
          memoryTotal,
          memoryUsed,
          gpuUtilization,
          temperature,
          fanSpeed,
          powerDraw,
          powerLimit,
          pcieGen,
          graphicsClock,
          memoryClock
        ] = line.split(', ').map(item => item.trim());
        
        // 计算内存使用率
        const memoryTotalBytes = parseInt(memoryTotal) * 1024 * 1024;
        const memoryUsedBytes = parseInt(memoryUsed) * 1024 * 1024;
        const memoryUtilization = memoryTotalBytes > 0 
          ? (memoryUsedBytes / memoryTotalBytes) * 100 
          : 0;
        
        // 计算功耗使用率
        const powerDrawNum = parseFloat(powerDraw);
        const powerLimitNum = parseFloat(powerLimit);
        const powerUtilization = powerLimitNum > 0 
          ? (powerDrawNum / powerLimitNum) * 100 
          : 0;
        
        gpuData.push({
          id: parseInt(index),
          name,
          driverVersion,
          status: '正常',
          memoryTotal: memoryTotalBytes,
          memoryUsed: memoryUsedBytes,
          memoryUtilization,
          gpuUtilization: parseInt(gpuUtilization),
          temperature: parseInt(temperature),
          fanSpeed: parseInt(fanSpeed),
          powerDraw: powerDrawNum,
          powerLimit: powerLimitNum,
          powerUtilization,
          pcieLink: `PCIe ${pcieGen}`,
          graphicsClock: parseInt(graphicsClock),
          memoryClock: parseInt(memoryClock),
          processes: []
        });
      }
      
      console.log('   ✓ 成功获取GPU信息:');
      gpuData.forEach(gpu => {
        console.log(`     GPU ${gpu.id}: ${gpu.name}`);
        console.log(`       显存使用: ${gpu.memoryUsed} / ${gpu.memoryTotal} bytes`);
        console.log(`       GPU使用率: ${gpu.gpuUtilization}%`);
        console.log(`       温度: ${gpu.temperature}°C`);
      });
    } catch (error) {
      console.log('   ✗ 获取GPU信息失败:', error.message);
    }
  } catch (error) {
    console.error('测试GPU Presenter功能时出错:', error);
  }
}

testGpuPresenterFunctionality();