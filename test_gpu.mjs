// 简单的测试脚本来验证GPU功能是否正常工作
import { exec } from 'child_process';
import { promisify } from 'util';

const execPromise = promisify(exec);

async function testGpuInfo() {
  try {
    console.log('检查NVIDIA驱动...');
    const version = await execPromise('nvidia-smi --version');
    console.log('NVIDIA驱动信息:', version.stdout);
    
    console.log('\n获取GPU信息...');
    const gpuInfo = await execPromise(
      'nvidia-smi --query-gpu=index,name,driver_version,memory.total,memory.used,utilization.gpu,temperature.gpu,fan.speed,power.draw,power.limit,pcie.link.gen.current,clocks.current.graphics,clocks.current.memory --format=csv,noheader,nounits'
    );
    
    console.log('GPU信息:');
    console.log(gpuInfo.stdout);
    
    // 解析GPU信息
    const lines = gpuInfo.stdout.trim().split('\n');
    for (const line of lines) {
      const [
        index,
        name,
        driverVersion,
        memoryTotal,
        memoryUsed,
        gpuUtilization,
        temperature,
        fanSpeed,
        powerDraw,
        powerLimit,
        pcieGen,
        graphicsClock,
        memoryClock
      ] = line.split(', ').map(item => item.trim());
      
      console.log(`\nGPU ${index}: ${name}`);
      console.log(`  驱动版本: ${driverVersion}`);
      console.log(`  显存: ${memoryUsed} MB / ${memoryTotal} MB`);
      console.log(`  GPU使用率: ${gpuUtilization}%`);
      console.log(`  温度: ${temperature}°C`);
      console.log(`  风扇转速: ${fanSpeed}%`);
      console.log(`  功耗: ${powerDraw}W / ${powerLimit}W`);
      console.log(`  PCIe连接: PCIe ${pcieGen}`);
      console.log(`  核心频率: ${graphicsClock} MHz`);
      console.log(`  显存频率: ${memoryClock} MHz`);
    }
  } catch (error) {
    console.error('测试GPU信息时出错:', error.message);
  }
}

testGpuInfo();