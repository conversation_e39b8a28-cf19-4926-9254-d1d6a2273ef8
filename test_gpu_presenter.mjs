// 简单的测试脚本来验证GPU Presenter是否正常工作
import { GpuPresenter } from './src/main/presenter/gpuPresenter/index.ts';

async function testGpuPresenter() {
  try {
    console.log('创建GPU Presenter实例...');
    const gpuPresenter = new GpuPresenter();
    
    console.log('检查GPU支持...');
    const isSupported = await gpuPresenter.isGpuMonitoringSupported();
    console.log('GPU支持:', isSupported);
    
    if (isSupported) {
      console.log('获取GPU信息...');
      const gpuInfo = await gpuPresenter.getNvidiaGpuInfo();
      console.log('GPU信息:', JSON.stringify(gpuInfo, null, 2));
    } else {
      console.log('GPU监控不支持');
    }
  } catch (error) {
    console.error('测试GPU Presenter时出错:', error);
  }
}

testGpuPresenter();